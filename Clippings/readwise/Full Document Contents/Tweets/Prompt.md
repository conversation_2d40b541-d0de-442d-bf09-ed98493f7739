---
人员:
  - "[[Augment]]"
tags:
  - tweets
日期: 2025-07-07
时间: None
相关:
  - "[[fix]]"
  - "[[run]]"
  - "[[rerun]]"
  - "[[write]]"
  - "[[agents]]"
  - "[[nudges]]"
  - "[[Clippings/readwise/Full Document Contents/Tweets/Prompt]]"
  - "[[refine]]"
  - "[[test 3]]"
  - "[[科技]]"
  - "[[clarify]]"
  - "[[confirm]]"
  - "[[correct]]"
  - "[[explain]]"
  - "[[failure]]"
  - "[[iterate]]"
  - "[[feedback]]"
  - "[[momentum]]"
  - "[[teammate]]"
  - "[[null case]]"
  - "[[run tests]]"
  - "[[workflows]]"
  - "[[in-session]]"
  - "[[twitter.com]]"
  - "[[Augment]]"
  - "[[tests failed]]"
  - "[[build context]]"
  - "[[feedback loop]]"
  - "[[misunderstood]]"
  - "[[off-by-one error]]"
  - "[[test-driven development]]"
链接: https://twitter.com/augmentcode/status/1942257652173877537/?rw_tt_thread=True
附件: https://pbs.twimg.com/profile_images/1849169714100269057/YF9iS2wt.png)
---
## Document Note

## Summary

本文介绍了如何通过反馈循环使 AI Agent 更加实用。传统上，Prompt 出错后往往选择重新开始，但作者建议采用类似测试驱动开发的思路：先写 Prompt，运行，发现错误，再修正，反复迭代。目标不是一次写出完美的 Prompt，而是通过快速的循环积累进展。失败是重要反馈，能揭示 Agent 理解错误、表达不清或需澄清的部分。保持同一会话环境，像与队友合作一样逐步调整，例如“这个差不多了，只需修正空值情况”，而非全盘推翻。这样几次小的调整就能显著提升效果。

通过这种方法，可以避免盲目猜测，利用失败信息不断改进 Prompt，快速提升 Agent 的表现和准确度。

---

**问题 1：**  
为什么作者建议采用反馈循环而不是直接重新开始写 Prompt？

答案：  
因为反馈循环能够利用失败信息，明确指出 Agent 理解错误和表达不清的地方，有针对性地修正，避免盲目猜测和浪费时间，从而更快积累进步。

**问题 2：**  
“测试驱动开发”思路在 Prompt 优化中具体表现为何？

答案：  
表现为“写→运行→修正→重新运行”的快速迭代过程，通过测试结果及时发现问题，逐步完善 Prompt，而不是一次性追求完美。

**问题 3：**  
保持同一会话环境对优化 Prompt 有何作用？

答案：  
保持上下文连续性方便逐步调整和纠正，类似与团队成员协作，能精细定位问题并局部修正，而非重新开始，提升效率和效果。

## Full Document
Prompt.   
Wait.   
It messes up.   
Start over? 

Not if you build a feedback loop.

Here’s how to make Agents actually useful 👇

---

The best Agent workflows look like test-driven development:

Write → run → fix → rerun. 

You’re not aiming for a perfect prompt—you're building momentum.

---

Let it write and run tests.

Then iterate:

“Tests failed—what went wrong?”  
“Fix the off-by-one error in test 3.”  
“Rerun and confirm.”

Quick cycles beat careful guesses every time.

---

Failure is feedback.

It tells you:   
– What the Agent misunderstood   
– What you didn’t explain   
– What to clarify next

Don’t bail—refine.

---

Stay in-session. Build context.

Correct it like you would a teammate:

“This is close—just fix the null case.”  
“Leave the rest as-is.”

You’ll be surprised how far a few nudges go.
