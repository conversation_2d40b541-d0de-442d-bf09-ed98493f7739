# Video-Factory GitHub Copilot 自定义指令

## 项目上下文

你正在协助开发Video-Factory项目，这是一个基于Python的多平台视频数据采集和同步工具。

### 项目特征
- **项目类型**: 数据采集与同步系统
- **主要功能**: 抖音/TikTok视频数据采集，飞书多维表格同步
- **技术栈**: Python 3.8+, TikHub API, 飞书API, requests, structlog
- **架构模式**: 模块化架构，职责分离
- **当前版本**: v3.8.0
- **用户水平**: 编程小白，需要详细解释

### 项目结构
```
Video-Factory/
├── src/                    # 核心模块
│   ├── business.py        # 业务逻辑
│   ├── processor.py       # 数据处理
│   ├── apis.py           # API调用
│   ├── config.py         # 配置管理
│   └── utils.py          # 工具函数
├── crawl.py              # 数据采集入口
├── upload.py             # 数据同步入口
├── data/                 # 数据存储
├── input/                # 输入文件
└── memory/               # 项目记忆库
```

## 编码标准和最佳实践

### Python编码规范
1. **遵循PEP 8标准**
2. **使用类型提示**：为函数参数和返回值添加类型注解
3. **详细注释**：每个函数都要有docstring，复杂逻辑要有行内注释
4. **错误处理**：使用try-except处理可能的异常
5. **日志记录**：使用structlog记录关键操作和错误

### 代码风格要求
```python
# 函数定义示例
def process_video_data(video_data: Dict[str, Any], platform: str) -> Dict[str, Any]:
    """
    处理视频数据，标准化格式
    
    Args:
        video_data: 原始视频数据
        platform: 平台类型 ('douyin' 或 'tiktok')
    
    Returns:
        标准化后的视频数据
    
    Raises:
        ValueError: 当平台类型不支持时
    """
    try:
        # 实现逻辑
        pass
    except Exception as e:
        logger.error("处理视频数据失败", error=str(e), platform=platform)
        raise
```

### 模块设计原则
1. **单一职责**：每个模块只负责一个特定功能
2. **依赖注入**：通过参数传递依赖，避免硬编码
3. **配置驱动**：所有可变参数通过配置文件管理
4. **向后兼容**：新功能不能破坏现有接口

## API集成规范

### TikHub API调用
```python
# 标准API调用模式
def call_tikhub_api(endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """调用TikHub API的标准模式"""
    headers = {
        'Authorization': f'Bearer {config.TIKHUB_API_KEY}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{config.TIKHUB_BASE_URL}/{endpoint}", 
                              params=params, headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        logger.error("TikHub API调用失败", endpoint=endpoint, error=str(e))
        raise
```

### 飞书API集成
```python
# 飞书API调用模式
def upload_to_feishu(table_id: str, records: List[Dict]) -> bool:
    """上传数据到飞书多维表格"""
    # 实现飞书API调用逻辑
    pass
```

## 数据处理规范

### 数据标准化
1. **统一字段命名**：使用snake_case命名
2. **类型转换**：确保数据类型一致性
3. **空值处理**：统一处理None和空字符串
4. **时间格式**：使用ISO 8601格式

### 去重机制
```python
# 标准去重模式
def check_duplicate(entity_id: str, entity_type: str) -> bool:
    """检查实体是否已存在"""
    # 基于ID的去重逻辑
    pass
```

## 错误处理和日志

### 日志记录标准
```python
import structlog

logger = structlog.get_logger()

# 信息日志
logger.info("开始处理视频数据", video_count=len(videos))

# 错误日志
logger.error("API调用失败", 
            endpoint=endpoint, 
            status_code=response.status_code,
            error_message=response.text)

# 调试日志
logger.debug("数据处理详情", 
            processed_count=count,
            skipped_count=skipped)
```

### 异常处理模式
```python
try:
    # 主要逻辑
    result = process_data(data)
except ValueError as e:
    logger.error("数据验证失败", error=str(e))
    raise
except requests.RequestException as e:
    logger.error("网络请求失败", error=str(e))
    # 可以选择重试或返回默认值
except Exception as e:
    logger.error("未知错误", error=str(e))
    raise
```

## 测试要求

### 单元测试
```python
import pytest
from unittest.mock import Mock, patch

def test_process_video_data():
    """测试视频数据处理功能"""
    # 准备测试数据
    test_data = {"id": "123", "title": "测试视频"}
    
    # 执行测试
    result = process_video_data(test_data, "douyin")
    
    # 验证结果
    assert result["id"] == "123"
    assert "platform" in result
```

### 集成测试
```python
@patch('src.apis.requests.get')
def test_api_integration(mock_get):
    """测试API集成"""
    # Mock API响应
    mock_response = Mock()
    mock_response.json.return_value = {"status": "success"}
    mock_get.return_value = mock_response
    
    # 执行测试
    result = call_api("test_endpoint")
    
    # 验证结果
    assert result["status"] == "success"
```

## 性能优化指导

### 批量处理
```python
def process_batch(items: List[Any], batch_size: int = 100) -> None:
    """批量处理数据，避免内存溢出"""
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        process_items(batch)
```

### 异步处理
```python
import asyncio
import aiohttp

async def fetch_data_async(urls: List[str]) -> List[Dict]:
    """异步获取数据，提升性能"""
    async with aiohttp.ClientSession() as session:
        tasks = [fetch_single_url(session, url) for url in urls]
        results = await asyncio.gather(*tasks)
        return results
```

## 代码生成指导

### 新功能开发流程
1. **理解需求**：明确功能目标和约束条件
2. **设计接口**：定义函数签名和数据结构
3. **实现逻辑**：编写核心业务逻辑
4. **添加测试**：编写单元测试和集成测试
5. **更新文档**：更新README和API文档

### 代码审查要点
1. **功能正确性**：代码是否实现了预期功能
2. **错误处理**：是否有完善的异常处理
3. **性能考虑**：是否有性能瓶颈
4. **代码可读性**：注释是否清晰，命名是否合理
5. **测试覆盖**：是否有足够的测试用例

## 特殊注意事项

### 编程小白友好
1. **详细解释**：解释每个技术决策的原因
2. **提供示例**：给出完整的代码示例
3. **分步指导**：将复杂任务分解为简单步骤
4. **错误预防**：提醒常见的错误和陷阱

### 项目特定要求
1. **保持兼容性**：新代码要与现有架构兼容
2. **配置管理**：使用.env文件管理配置
3. **模块化设计**：新功能要符合现有模块结构
4. **文档更新**：重要变更要更新README文档
