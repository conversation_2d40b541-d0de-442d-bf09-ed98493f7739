# Ming-Digital-Garden Augment 专用规则

## 项目上下文感知

### 自动识别工作区域
当用户提及路径或项目时，自动识别所属模块：
- `11-*`: 视频内容相关项目
- `12-*`: 数据迁移项目  
- `13-*`: 提示词工程项目
- `14-*`: 学习课程项目
- `20-*`: 知识库项目
- `30-*`: 资源管理项目

### 上下文切换规则
```
IF 用户切换到新项目目录 THEN
    1. 加载对应项目的记忆库
    2. 检查项目特定的技术栈
    3. 应用项目级别的编码规范
    4. 更新active_context.md
```

## 三阶段工作流程适配

### 规划阶段 (PLAN)
**触发条件**:
- 新功能开发请求
- 跨项目集成需求
- 架构变更建议

**执行流程**:
1. 检查相关项目记忆库
2. 分析对数字花园整体的影响
3. 考虑与现有提示词工程的集成
4. 提出详细实施计划
5. 请求用户确认

### 实施阶段 (CODE)
**质量标准**:
- 遵循项目特定的编码规范
- 保持与现有架构的一致性
- 更新相关文档和记忆库
- 考虑跨项目的影响

**测试要求**:
- 功能测试
- 集成测试（如果涉及多个项目）
- 文档更新验证

### 调试阶段 (DEBUG)
**诊断范围**:
- 单项目内部问题
- 跨项目集成问题
- 工作流程优化建议

**记录要求**:
- 更新error-documentation.md
- 记录解决方案到lessons-learned.md
- 优化相关规则和流程

## 数字花园特定规则

### 文档管理
- 所有新文档必须包含YAML front matter
- 使用统一的标签体系
- 保持版本号的一致性

### 提示词工程集成
- 新开发的功能考虑提示词优化
- 将成功的工作流程抽象为可复用的提示词
- 定期更新提示词版本

### 知识沉淀
- 每次重要变更后更新相关记忆库
- 将解决方案模式化
- 建立最佳实践库

## 工具集成规范

### MCP工具使用
- 优先使用已配置的MCP工具
- 新工具集成需要更新配置文档
- 保持工具链的一致性

### 版本控制
- 重要变更前创建分支
- 提交信息遵循约定格式
- 定期同步远程仓库

### 备份策略
- 重要配置文件的备份
- 记忆库的定期导出
- 关键提示词的版本管理
