# Ming-Digital-Garden 工作流程模板

## 模板1: 新项目启动工作流

### 用户输入示例
```
"我想在数字花园中创建一个新的AI工具评测项目"
```

### AI响应模板
```
[模式：项目规划师🏗️]

我来帮你在数字花园中规划这个AI工具评测项目！

**第一步：项目定位分析**
基于你的数字花园结构，我建议：
- 项目编号：15-AI-Tool-Evaluation
- 位置：与现有的13-Prompt-Design形成互补
- 关联：可以为11-Video-Factory提供工具选型支持

**第二步：记忆库初始化**
我需要为这个项目创建专用记忆库，包括：
- 评测标准和方法论
- 工具分类体系
- 测试用例库

**第三步：集成规划**
考虑与现有项目的集成：
- 评测结果可以转化为提示词优化建议
- 为视频工厂项目提供工具推荐

你希望我现在开始创建项目结构吗？
```

## 模板2: 跨项目功能开发工作流

### 用户输入示例
```
"我想让Video-Factory项目能够自动生成提示词来优化视频标题"
```

### AI响应模板
```
[模式：架构师+工程师🔧]

这是一个很棒的跨项目集成需求！让我分析一下：

**影响范围分析**
- 主项目：11-Video-Factory
- 关联项目：13-Prompt-Design
- 新增依赖：可能需要AI API集成

**技术方案设计**
1. 在Video-Factory中添加prompt_generator模块
2. 从Prompt-Design项目复用标题优化模板
3. 建立配置文件管理不同平台的标题规则

**实施计划**
阶段1：设计接口和数据流
阶段2：实现核心功能
阶段3：集成测试和优化

**记忆库更新计划**
- 更新Video-Factory的技术文档
- 在Prompt-Design中记录新的应用场景
- 创建跨项目集成的最佳实践文档

准备好开始实施了吗？
```

## 模板3: 问题诊断工作流

### 用户输入示例
```
"我的提示词在不同AI工具中效果差异很大"
```

### AI响应模板
```
[模式：诊断专家🔍]

让我帮你系统性地诊断这个问题：

**问题上下文收集**
1. 涉及的AI工具：[需要用户确认]
2. 具体的提示词：[需要查看]
3. 期望效果 vs 实际效果：[需要对比]

**诊断计划**
1. 检查你的13-Prompt-Design项目中的相关模板
2. 分析不同AI工具的特性差异
3. 查找已有的最佳实践文档

**解决方案方向**
- 创建多版本适配的提示词模板
- 建立AI工具特性对比文档
- 优化你的提示词工程流程

让我先查看一下你现有的提示词资源，然后制定具体的优化方案。
```

## 模板4: 知识沉淀工作流

### 触发场景
每次完成重要任务后的知识整理

### 执行步骤
```
1. 更新项目记忆库
   - 记录新的解决方案
   - 更新技术文档
   - 添加经验教训

2. 提取可复用模式
   - 识别通用解决方案
   - 创建模板文件
   - 更新最佳实践库

3. 跨项目知识传播
   - 检查其他项目的适用性
   - 更新相关文档
   - 建立知识链接

4. 版本控制
   - 提交变更
   - 更新版本号
   - 创建变更日志
```

## 快速启动命令

### 项目切换
```
@切换到 [项目名称]
→ 自动加载项目记忆库和上下文
```

### 工作流程启动
```
@规划模式 [需求描述]
@实施模式 [任务描述]  
@诊断模式 [问题描述]
```

### 记忆库操作
```
@更新记忆库 [项目名称]
@查看上下文 [项目名称]
@同步知识库
```
