# Ming-Digital-Garden 记忆库设置指南

## 项目结构映射

基于你的数字花园结构，建议建立以下记忆库层次：

### 根级记忆库 (/)
```
memory/
├── docs/
│   ├── product_requirement_docs.md     # 数字花园整体PRD
│   ├── architecture.md                 # 整体架构设计
│   └── technical.md                    # 技术规范
└── tasks/
    ├── tasks_plan.md                   # 全局任务计划
    └── active_context.md               # 当前活跃上下文
```

### 项目级记忆库
每个主要项目目录下建立独立记忆库：

#### 11-Video-Factory/memory/
- 视频工厂项目的专用记忆库
- 包含爬虫、上传、平台配置等上下文

#### 13-Prompt-Design/memory/
- 提示词工程项目记忆库
- 管理各版本提示词的演进历史

#### 14-PKM-Course/memory/
- 个人知识管理课程记忆库
- 追踪学习进度和实践成果

## 记忆库文件模板

### 1. product_requirement_docs.md
```markdown
# Ming-Digital-Garden 产品需求文档

## 项目愿景
构建一个集成化的数字花园生态系统，支持：
- 知识管理与沉淀
- AI辅助开发工作流
- 多媒体内容创作
- 个人学习与成长

## 核心功能模块
1. **提示词工程中心** (13-Prompt-Design)
2. **视频内容工厂** (11-Video-Factory)  
3. **知识管理系统** (14-PKM-Course)
4. **资源库管理** (20-Library, 30-Resources)

## 技术栈
- Python (数据处理、爬虫)
- Markdown (文档管理)
- AI工具集成 (Augment, MCP)
```

### 2. architecture.md
```markdown
# 系统架构设计

## 目录架构
- 数字编号前缀分类系统
- 模板化文档结构
- 版本控制集成

## AI协作架构
- Augment Agent 作为主要开发助手
- MCP工具链集成
- 结构化工作流程

## 数据流设计
输入 → 处理 → 输出 → 反馈 → 优化
```

### 3. technical.md
```markdown
# 技术规范

## 开发环境
- VSCode + Augment
- Python 虚拟环境管理
- Git版本控制

## 编码规范
- 遵循PEP 8 (Python)
- Markdown文档标准化
- 文件命名约定

## AI协作规范
- 使用结构化工作流程
- 保持记忆库同步
- 定期更新上下文
```
