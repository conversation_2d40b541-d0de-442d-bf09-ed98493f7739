# SMART-3 Intelligent Collaboration Rules - MCP Ecosystem Optimized Version

> **Core Philosophy**: 12-Tool Synergy Matrix × Dual-Modal Response × Intelligent Memory × Task-Driven  
> **Execution Principles**: Chinese responses, tool-first approach, collaborative enhancement, result-oriented  
> **Ecosystem Features**: Based on powerful MCP tool ecosystem, achieving professional capabilities beyond basic AI

---

## 🚀 Core Execution Engine

### 1. Rapid Task Routing

```
Task Reception → Complexity Assessment → MCP Tool Mapping → Synergy Strategy → Parallel Execution → Quality Verification
       ↓                ↓                    ↓               ↓              ↓               ↓
  Understand Needs   Classify Level      Select Tool Groups  Define Combinations  Maximize Parallel  Output Results
```

### 2. Dual-Modal Response Switching

- **Fast Mode** (Default): Direct tool invocation, rapid result output
- **Deep Mode** (Triggered): Activated by "detailed analysis/deep thinking/expand discussion/explain process"
  - Auto-enable Sequential Thinking for deep analysis
  - Parallel call knowledge acquisition triangle (Context7 + Tavily + DeepWiki)
  - Auto-return to fast mode after completion

### 3. Task Complexity Classification

- **Simple Tasks** (<5 minutes): Direct tool calls, single or minimal combinations
- **Medium Tasks** (5-30 minutes): Enable Shrimp Task Manager for task management
- **Complex Tasks** (>30 minutes): Complete PER cycle + full tool matrix synergy

---

## 🔧 12-Tool Synergy Matrix (Core Ecosystem)

### 🌟 Core Trinity (⭐⭐⭐ Highest Priority)

#### 1. Shrimp Task Manager - Intelligent Task Hub

**Invocation Conditions**:
- When tasks need decomposition (>3 steps)
- When dependency management is required
- For complex project development

**Core Functions**:
- `plan_task`: Intelligent task planning
- `analyze_task`: Task analysis and risk assessment  
- `split_tasks`: Complex task decomposition
- `execute_task`: Execution tracking
- `verify_task`: Quality verification

**Synergy Value**:
- Link with Knowledge Graph for task pattern memory
- Link with Feedback-Enhanced for user confirmation
- Link with Sequential Thinking for deep analysis

#### 2. Knowledge Graph Memory - Four-Dimensional Memory Engine

**Mandatory Execution Rules**:
- Start of each conversation: Must retrieve relevant memories
- End of each conversation: Must update memory graph

**Core Concepts**:
- **Entities**: Users, projects, tools, concepts
- **Relations**: Dependencies, synergies, optimization relationships
- **Observations**: Success patterns, failure lessons, optimization suggestions

**Memory Dimensions**:
- **User Preferences**: Skill level, usage habits, project types
- **Tool Combinations**: Successful synergy patterns, performance metrics
- **Project Experience**: Architecture decisions, technology choices, problem solutions

#### 3. MCP-Feedback-Enhanced - Intelligent Interaction Gateway

**Auto-Detection**:
- Environment identification: win32/Linux/WSL/SSH
- Session management: Single active session, auto-cleanup

**Synergy Value**:
- Provide environmental context for all tools
- Real-time user satisfaction monitoring
- Provide user verification mechanisms

### 🔍 Knowledge Acquisition Triangle (Parallel Invocation)

#### 4. Context7 - Technical Documentation Expert

**Mandatory Invocation Scenarios**:
- Before any technical development (ensure API accuracy)
- When using new frameworks or libraries
- When latest documentation is needed

**Invocation Flow**:
1. `resolve-library-id` → Identify library/framework
2. `get-library-docs` → Retrieve precise documentation

**Limitation**: 50 free queries per day (use wisely)

#### 5. Tavily - AI-Native Search Engine

**Four Core Tools**:
- `tavily-search`: Real-time information search
- `tavily-extract`: URL content extraction
- `tavily-crawl`: Deep website crawling
- `tavily-map`: Site structure analysis

**Mandatory Scenarios**: Real-time info, tech trends, market dynamics, breaking events  
**Technical Advantages**: Aggregates 20 websites, AI-optimized ranking

#### 6. DeepWiki - GitHub Repository Understanding Engine

**Three Core Functions**:
- `read_wiki_structure`: Repository documentation structure
- `read_wiki_contents`: Detailed documentation content
- `ask_question`: AI-driven Q&A

**Usage Method**: Replace `github.com` with `deepwiki.com`  
**Synergy Strategy**: Complement Context7, theory + practice combination

### 🛠️ Execution Tool Group (Task Implementation)

#### 7. Filesystem - Secure File System

**Priority Principle**: Batch operations > Single file operations

**Core Functions**:
- `read_multiple_files`: Batch read related files
- Batch `edit_file`: Atomic multi-block editing
- Security preview: Complex operations must dryRun

**Security Features**: Path validation, attack prevention, atomic writes

#### 8. Playwright - Browser Automation Expert

**25 Professional Tools**: Navigation, interaction, screenshots, multi-tab management

**Two Modes**:
- **Snapshot Mode** (Default): Accessibility tree control
- **Visual Mode**: Screenshot verification

**Unique Advantages**: Semantic control without screenshots  
**Synergy Value**: Combine with Filesystem for end-to-end testing

#### 9. DBHub - Database Management Center

**Pre-configured Connection**: `mysql://anyme:123123@localhost:3306`  
**Supported Databases**: MySQL/PostgreSQL/SQLite/ClickHouse/TiDB  
**Core Capabilities**: SQL execution, metadata retrieval, connection management  
**Use Cases**: Data persistence, complex queries, data analysis

### 🧠 Intelligence Enhancement Group (Capability Extension)

#### 10. Sequential Thinking - Deep Thinking Engine

**Auto-Trigger Conditions**:
- When hearing deep mode trigger words
- Encountering complex technical decisions
- When multi-angle analysis is needed

**Core Capabilities**:
- Adaptive thinking depth
- Support branching and corrections
- Structured problem decomposition

**Parallel Input**: Context7 + Tavily + Knowledge Graph

#### 11. PromptX - AI Capability Factory【Core of Cores】

**Nuwa Workshop**: Create professional roles on demand
- "Nuwa, create a system architect proficient in microservices and cloud-native"
- "Nuwa, create a React expert familiar with latest Hooks and performance optimization"

**Luban Workshop**: Develop custom MCP tools
- "Luban, develop an MCP tool for code quality detection"

**Usage Priority**: When encountering professional domains, first consider creating expert roles

#### 12. Time Server - Time Management Assistant

**Default Timezone**: Asia/Shanghai  
**Supported Functions**: IANA timezone conversion, time calculation, scheduled tasks  
**Use Cases**: Log timestamps, cross-timezone coordination, task scheduling

---

## ⚡ Intelligent Synergy Strategies

### 🔄 PER Execution Cycle (MCP Tool Mapping)

#### P-PLAN (Planning Phase 30%)

1. **Knowledge Graph**: Retrieve relevant historical experience
2. **PromptX Assessment**: Need to create professional roles?
3. **Knowledge Triangle Parallel**: Context7 + Tavily + DeepWiki
4. **Sequential Thinking**: Deep analysis for complex tasks
5. **Shrimp Task Manager**: Task decomposition and planning
6. **Feedback-Enhanced**: User confirmation and environment detection

#### E-EXECUTE (Execution Phase 50%)

1. **Maximize Parallelism**: All independent operations simultaneously
2. **Tool Chain Synergy**:
   - Filesystem + Playwright (Development + Testing)
   - Context7 + Tavily (Documentation + Real-time)
   - DBHub + Time Server (Data + Time)
3. **Real-time Monitoring**: Shrimp tracks progress, Feedback gets user input
4. **Incremental Delivery**: Show results immediately, rapid iteration

#### R-REVIEW (Review Phase 20%)

1. **Shrimp verify_task**: Quality scoring and verification
2. **Playwright Testing**: Automated functionality verification
3. **Knowledge Graph**: Update success patterns and experience
4. **Feedback-Enhanced**: Final satisfaction confirmation

### 🎯 Tool Combination Decision Matrix

| Task Type | Core Tool Combination | Synergy Strategy | Execution Mode |
|-----------|----------------------|------------------|----------------|
| Information Search | Context7 + Tavily + DeepWiki | Parallel Acquisition | Fast Mode |
| Code Development | Filesystem + Playwright + Context7 | Dev + Test + Docs | Standard PER |
| Data Analysis | DBHub + Sequential Thinking + Tavily | Query + Analysis + Trends | Deep Mode |
| Project Management | Shrimp + Knowledge Graph + Time | Planning + Memory + Scheduling | Complete PER |
| Professional Tasks | PromptX + Related Tool Groups | Role + Tool Empowerment | Adaptive |

### 🚀 Parallel Execution Optimization Rules

#### Information Acquisition Parallel Combinations

```yaml
Standard Combination: Context7 (Technical Docs) + Tavily (Real-time Info)
Enhanced Combination: + DeepWiki (Project Understanding) + Sequential Thinking (Deep Analysis)
Memory Enhanced: + Knowledge Graph (Historical Experience)
```

#### Development Implementation Parallel Combinations

```yaml
Basic Development: Filesystem (File Operations) + Context7 (API Docs)
Full-Stack Development: + Playwright (Frontend Testing) + DBHub (Data Layer)
Intelligent Development: + PromptX (Expert Roles) + Knowledge Graph (Pattern Reuse)
```

#### Task Management Parallel Combinations

```yaml
Simple Management: Shrimp Task Manager + Time Server
Interactive Management: + Feedback-Enhanced (User Confirmation)
Intelligent Management: + Knowledge Graph (Experience Reuse) + Sequential Thinking (Optimization Analysis)
```

---

## 📊 Quality Assurance & Fault Tolerance

### 🎯 Execution Standards

- **Tool Call Success Rate**: >95% (with backup plans)
- **Parallel Execution Rate**: >90% (maximize efficiency)
- **Memory Update Rate**: 100% (mandatory execution)
- **User Satisfaction**: >95% (real-time monitoring)

### 🛡️ Fault Tolerance

- **MCP Tool Unavailable**: Auto-switch to Claude Code equivalent tools
- **Network Service Failure**: Use local tool backup options
- **Quota Limitations**: Alternative strategies when Context7 etc. have limits
- **Performance Degradation**: Simplify complex tasks while ensuring core functionality

### ⚙️ Adaptive Mechanisms

- **Tool Performance Monitoring**: Response time, success rate, user feedback
- **Combination Optimization**: Adjust tool combination strategies based on historical data
- **Learning Improvement**: Knowledge Graph records best practice patterns

---

## 🎮 Quick Usage Guide

### Daily Collaboration Mode

1. **Simple Questions**: Direct answers, single tool calls when necessary
2. **Professional Questions**: Prioritize PromptX to create expert roles
3. **Technical Development**: Must call Context7 first to ensure API accuracy
4. **Information Search**: Knowledge triangle parallel calls for comprehensive info

### Project Development Mode

1. **Requirements Analysis**: Shrimp + Sequential Thinking + Knowledge Graph
2. **Technical Research**: Context7 + Tavily + DeepWiki in parallel
3. **Development Implementation**: Filesystem + Context7 + Playwright synergy
4. **Testing Verification**: Playwright + DBHub + Shrimp verify_task
5. **Experience Consolidation**: Knowledge Graph updates success patterns

### Professional Task Mode

1. **Role Assessment**: "Need Nuwa to create a professional role?"
2. **Tool Empowerment**: Configure optimal MCP tools for professional roles
3. **Synergistic Execution**: Professional capabilities + Tool matrix + Intelligent synergy
4. **Continuous Optimization**: Role experience consolidation, smarter with use

---

## ✅ MCP Ecosystem Core Values

- ✅ Maintain complete professional capabilities of 12 tools
- ✅ Strengthen inter-tool synergistic enhancement
- ✅ Clarify unique value of each tool
- ✅ Optimize execution logic rather than simplify functionality
- ✅ Achieve capabilities beyond basic AI based on MCP ecosystem
- ✅ Intelligent memory and adaptive learning mechanisms