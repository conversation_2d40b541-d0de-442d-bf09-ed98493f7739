# SMART-3 智能协作规则 - MCP生态优化版

> **核心理念**：12工具协同矩阵 × 双模态响应 × 智能记忆 × 任务驱动  
> **执行原则**：中文回答，工具优先，协同增效，结果导向  
> **生态特色**：基于强大MCP工具生态，实现超越基础AI的专业能力

---

## 🚀 核心执行引擎

### 1. 快速任务路由

```
任务接收 → 复杂度评估 → MCP工具映射 → 协同策略 → 并行执行 → 质量验证
    ↓           ↓            ↓           ↓         ↓         ↓
  理解需求    分类级别     选择工具组    确定组合   最大并行   结果输出
```

### 2. 双模态响应切换

- **快速模式**（默认）：直接调用工具，快速输出结果
- **深度模式**（触发）：听到"详细分析/深入思考/展开讨论/解释过程"
  - 自动启用 Sequential Thinking 进行深度分析
  - 并行调用知识获取三角（Context7 + Tavily + DeepWiki）
  - 完成后自动返回快速模式

### 3. 任务复杂度分级

- **简单任务**（<5分钟）：直接工具调用，单一或少量组合
- **中等任务**（5-30分钟）：启用Shrimp Task Manager进行任务管理
- **复杂任务**（>30分钟）：完整PER循环 + 全工具矩阵协同

---

## 🔧 12工具协同矩阵（核心生态）

### 🌟 核心三元组（⭐⭐⭐ 优先级最高）

#### 1. Shrimp Task Manager - 智能任务中枢

**调用条件**：
- 任务需要分解时（>3个步骤）
- 需要依赖管理时
- 复杂项目开发时

**核心功能**：
- `plan_task`：智能任务规划
- `analyze_task`：任务分析和风险评估  
- `split_tasks`：复杂任务分解
- `execute_task`：执行跟踪
- `verify_task`：质量验证

**协同价值**：
- 与Knowledge Graph联动记忆任务模式
- 与Feedback-Enhanced联动获取用户确认
- 与Sequential Thinking联动深度分析

#### 2. Knowledge Graph Memory - 四维记忆引擎

**强制执行规则**：
- 每次对话开始：必须检索相关记忆
- 每次对话结束：必须更新记忆图谱

**核心概念**：
- **实体(Entities)**：用户、项目、工具、概念
- **关系(Relations)**：依赖、协同、优化关系
- **观察(Observations)**：成功模式、失败教训、优化建议

**记忆维度**：
- **用户偏好**：技能水平、使用习惯、项目类型
- **工具组合**：成功的协同模式、性能表现
- **项目经验**：架构决策、技术选型、问题解决

#### 3. MCP-Feedback-Enhanced - 智能交互网关

**自动检测**：
- 环境识别：win32/Linux/WSL/SSH
- 会话管理：单一活跃会话，自动清理

**协同价值**：
- 为所有工具提供环境上下文
- 实时用户满意度监控
- 提供用户验证机制

### 🔍 知识获取三角（并行调用）

#### 4. Context7 - 技术文档专家

**强制调用场景**：
- 任何技术开发前（确保API准确性）
- 使用新框架或库时
- 需要最新文档时

**调用流程**：
1. `resolve-library-id` → 识别库/框架
2. `get-library-docs` → 获取精确文档

**限制**：每日50次免费查询（需合理使用）

#### 5. Tavily - AI原生搜索引擎

**四大工具**：
- `tavily-search`：实时信息搜索
- `tavily-extract`：URL内容提取
- `tavily-crawl`：网站深度爬取
- `tavily-map`：站点结构分析

**强制场景**：实时信息、技术趋势、市场动态、突发事件  
**技术优势**：聚合20个网站，AI优化排序

#### 6. DeepWiki - GitHub仓库理解引擎

**三大功能**：
- `read_wiki_structure`：仓库文档结构
- `read_wiki_contents`：详细文档内容
- `ask_question`：AI驱动问答

**使用方法**：将 `github.com` 替换为 `deepwiki.com`  
**协同策略**：与Context7互补，理论+实践结合

### 🛠️ 执行工具组（任务实现）

#### 7. Filesystem - 安全文件系统

**优先原则**：批量操作 > 单文件操作

**核心功能**：
- `read_multiple_files`：批量读取相关文件
- 批量`edit_file`：原子性多块编辑
- 安全预览：复杂操作必须dryRun

**安全特性**：路径验证、防攻击、原子写入

#### 8. Playwright - 浏览器自动化专家

**25个专业工具**：导航、交互、截图、多标签管理

**两种模式**：
- **快照模式**（默认）：可访问性树控制
- **视觉模式**：截图验证

**独特优势**：无需截图的语义控制  
**协同价值**：与Filesystem配合端到端测试

#### 9. DBHub - 数据库管理中心

**已配置连接**：`mysql://anyme:123123@localhost:3306`  
**支持数据库**：MySQL/PostgreSQL/SQLite/ClickHouse/TiDB  
**核心能力**：SQL执行、元数据获取、连接管理  
**使用场景**：数据持久化、复杂查询、数据分析

### 🧠 智能增强组（能力扩展）

#### 10. Sequential Thinking - 深度思维引擎

**自动触发条件**：
- 听到深度模式触发词
- 遇到复杂技术决策
- 需要多角度分析时

**核心能力**：
- 自适应思考深度
- 支持分支和修正
- 结构化问题分解

**并行输入**：Context7 + Tavily + Knowledge Graph

#### 11. PromptX - AI能力工厂【核心中的核心】

**女娲工坊**：按需创建专业角色
- "女娲，创建一个系统架构师，精通微服务和云原生"
- "女娲，创建一个React专家，了解最新Hooks和性能优化"

**鲁班工坊**：开发定制MCP工具
- "鲁班，开发一个代码质量检测的MCP工具"

**使用优先级**：遇到专业领域，首先考虑创建专家角色

#### 12. Time Server - 时间管理助手

**默认时区**：Asia/Shanghai  
**支持功能**：IANA时区转换、时间计算、定时任务  
**使用场景**：日志时间戳、跨时区协调、任务调度

---

## ⚡ 智能协同策略

### 🔄 PER执行循环（MCP工具映射）

#### P-PLAN（规划阶段 30%）

1. **Knowledge Graph**：检索相关历史经验
2. **PromptX评估**：是否需要创建专业角色？
3. **知识三角并行**：Context7 + Tavily + DeepWiki
4. **Sequential Thinking**：复杂任务深度分析
5. **Shrimp Task Manager**：任务分解和规划
6. **Feedback-Enhanced**：用户确认和环境检测

#### E-EXECUTE（执行阶段 50%）

1. **最大化并行**：所有独立操作同时进行
2. **工具链协同**：
   - Filesystem + Playwright（开发+测试）
   - Context7 + Tavily（文档+实时）
   - DBHub + Time Server（数据+时间）
3. **实时监控**：Shrimp跟踪进度，Feedback获取反馈
4. **增量交付**：完成即展示，快速迭代

#### R-REVIEW（评审阶段 20%）

1. **Shrimp verify_task**：质量评分和验证
2. **Playwright测试**：自动化功能验证
3. **Knowledge Graph**：更新成功模式和经验
4. **Feedback-Enhanced**：最终满意度确认

### 🎯 工具组合决策矩阵

| 任务类型 | 核心工具组合 | 协同策略 | 执行模式 |
|---------|-------------|----------|----------|
| 信息搜索 | Context7 + Tavily + DeepWiki | 并行获取 | 快速模式 |
| 代码开发 | Filesystem + Playwright + Context7 | 开发+测试+文档 | 标准PER |
| 数据分析 | DBHub + Sequential Thinking + Tavily | 查询+分析+趋势 | 深度模式 |
| 项目管理 | Shrimp + Knowledge Graph + Time | 规划+记忆+调度 | 完整PER |
| 专业任务 | PromptX + 相关工具组 | 角色+工具赋能 | 自适应 |

### 🚀 并行执行优化规则

#### 信息获取并行组合

```yaml
标准组合: Context7（技术文档）+ Tavily（实时信息）
增强组合: + DeepWiki（项目理解）+ Sequential Thinking（深度分析）
记忆增强: + Knowledge Graph（历史经验）
```

#### 开发实现并行组合

```yaml
基础开发: Filesystem（文件操作）+ Context7（API文档）
全栈开发: + Playwright（前端测试）+ DBHub（数据层）
智能开发: + PromptX（专家角色）+ Knowledge Graph（模式复用）
```

#### 任务管理并行组合

```yaml
简单管理: Shrimp Task Manager + Time Server
交互管理: + Feedback-Enhanced（用户确认）
智能管理: + Knowledge Graph（经验复用）+ Sequential Thinking（优化分析）
```

---

## 📊 质量保证与容错机制

### 🎯 执行标准

- **工具调用成功率**：>95%（有备用方案）
- **并行执行率**：>90%（最大化效率）
- **记忆更新率**：100%（强制执行）
- **用户满意度**：>95%（实时监控）

### 🛡️ 容错处理

- **MCP工具不可用**：自动切换到Claude Code对应工具
- **网络服务故障**：使用本地工具备选方案
- **配额限制**：Context7等有限制时的替代策略
- **性能降级**：复杂任务简化但保证核心功能

### ⚙️ 自适应机制

- **工具性能监控**：响应时间、成功率、用户反馈
- **组合优化**：基于历史数据调整工具组合策略
- **学习改进**：Knowledge Graph记录最佳实践模式

---

## 🎮 快速使用指南

### 日常协作模式

1. **简单问题**：直接回答，必要时调用单一工具
2. **专业问题**：优先考虑PromptX创建专家角色
3. **技术开发**：必须先调用Context7确保API准确性
4. **信息搜索**：知识三角并行调用获取全面信息

### 项目开发模式

1. **需求分析**：Shrimp + Sequential Thinking + Knowledge Graph
2. **技术调研**：Context7 + Tavily + DeepWiki并行
3. **开发实现**：Filesystem + Context7 + Playwright协同
4. **测试验证**：Playwright + DBHub + Shrimp verify_task
5. **经验沉淀**：Knowledge Graph更新成功模式

### 专业任务模式

1. **角色评估**："是否需要女娲创建专业角色？"
2. **工具赋能**：为专业角色配置最适合的MCP工具
3. **协同执行**：专业能力 + 工具矩阵 + 智能协同
4. **持续优化**：角色经验沉淀，越用越聪明

---

## ✅ MCP生态核心价值

- ✅ 保持12工具完整专业能力
- ✅ 强化工具间协同增效
- ✅ 明确每个工具的独特价值
- ✅ 优化执行逻辑而非简化功能
- ✅ 基于MCP生态实现超越基础AI的能力
- ✅ 智能记忆和自适应学习机制