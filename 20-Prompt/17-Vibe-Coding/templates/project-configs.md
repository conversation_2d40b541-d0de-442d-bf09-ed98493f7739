# Ming-Digital-Garden 项目配置文件

## 项目映射配置

### 11-Video-Factory
```yaml
project_type: "media_processing"
tech_stack: ["Python", "FFmpeg", "API Integration"]
dependencies: ["requests", "python-dotenv", "pathlib"]
memory_location: "11-Video-Factory/memory/"
coding_standards: "PEP8, async/await patterns"
test_framework: "pytest"
integration_points:
  - "13-Prompt-Design (title optimization)"
  - "30-Resources (platform configs)"
```

### 13-Prompt-Design  
```yaml
project_type: "ai_engineering"
tech_stack: ["Markdown", "YAML", "Prompt Engineering"]
dependencies: ["template_engine", "version_control"]
memory_location: "13-Prompt-Design/memory/"
coding_standards: "Structured prompts, version tracking"
test_framework: "manual_validation"
integration_points:
  - "11-Video-Factory (content optimization)"
  - "14-PKM-Course (learning templates)"
```

### 14-PKM-Course
```yaml
project_type: "knowledge_management"
tech_stack: ["Markdown", "Obsidian", "Tana"]
dependencies: ["conversion_scripts", "data_migration"]
memory_location: "14-PKM-Course/memory/"
coding_standards: "Structured notes, linking conventions"
test_framework: "content_validation"
integration_points:
  - "12-Logseq-Migration (data conversion)"
  - "20-Library (knowledge storage)"
```

## 工作流程配置

### 规划阶段配置
```yaml
plan_triggers:
  - "new_feature_request"
  - "cross_project_integration"
  - "architecture_change"
  
plan_checklist:
  - "check_project_memory"
  - "analyze_dependencies"
  - "consider_integration_impact"
  - "validate_with_existing_patterns"
  
plan_outputs:
  - "detailed_implementation_plan"
  - "resource_requirements"
  - "timeline_estimation"
  - "risk_assessment"
```

### 实施阶段配置
```yaml
code_quality_gates:
  - "syntax_validation"
  - "style_compliance"
  - "documentation_update"
  - "test_coverage"
  
integration_checks:
  - "cross_project_compatibility"
  - "memory_bank_updates"
  - "template_consistency"
  
delivery_criteria:
  - "functional_requirements_met"
  - "documentation_complete"
  - "tests_passing"
  - "integration_verified"
```

### 调试阶段配置
```yaml
debug_scope:
  - "single_project_issues"
  - "cross_project_conflicts"
  - "workflow_optimization"
  
diagnostic_tools:
  - "log_analysis"
  - "dependency_checking"
  - "configuration_validation"
  
resolution_tracking:
  - "error_documentation_update"
  - "lessons_learned_capture"
  - "process_improvement_suggestions"
```

## AI助手行为配置

### 响应模式
```yaml
communication_style:
  language: "简体中文"
  technical_terms: "保留英文"
  tone: "专业友好"
  
mode_indicators:
  planning: "[模式：项目规划师🏗️]"
  coding: "[模式：工程师🔧]"
  debugging: "[模式：诊断专家🔍]"
  research: "[模式：研究员📚]"
```

### 工具使用优先级
```yaml
primary_tools:
  - "codebase-retrieval"
  - "str-replace-editor"
  - "view"
  - "save-file"
  
secondary_tools:
  - "web-search"
  - "launch-process"
  - "browser_*"
  
integration_tools:
  - "github_*"
  - "firecrawl_*"
```

### 记忆库管理
```yaml
memory_update_triggers:
  - "task_completion"
  - "error_resolution"
  - "new_pattern_discovery"
  - "configuration_change"
  
memory_sync_frequency: "after_each_session"
memory_backup_strategy: "version_controlled"
```

## 自定义命令配置

### 快捷命令
```yaml
commands:
  "@switch": "切换项目上下文"
  "@plan": "启动规划模式"
  "@code": "启动实施模式"
  "@debug": "启动调试模式"
  "@sync": "同步记忆库"
  "@status": "查看项目状态"
```

### 自动化规则
```yaml
auto_actions:
  on_project_switch:
    - "load_project_memory"
    - "check_active_tasks"
    - "update_context"
  
  on_task_completion:
    - "update_memory_bank"
    - "check_integration_impact"
    - "suggest_next_steps"
  
  on_error_detection:
    - "capture_error_context"
    - "suggest_diagnostic_steps"
    - "update_error_documentation"
```
