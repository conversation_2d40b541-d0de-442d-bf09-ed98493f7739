# Augment Code Editor Best Practices

This document outlines the best practices for using the Augment code editor, with a special focus on the `.augment-guidelines.md` file. This file acts as a "manual" for the AI, helping it understand your project's specific conventions and architecture.

---

### The Core Philosophy: Create a Manual for the AI

The primary purpose of the `.augment-guidelines.md` file is to provide a continuously updated set of instructions for Augment's AI. When you ask Augment to perform a task like "add a new feature" or "refactor this component," it will first consult this file to ensure its output aligns with your project's standards. This dramatically improves the quality and consistency of the generated code.

### How to Use `.augment-guidelines.md`

#### 1. File Location and Format
*   **Filename**: `.augment-guidelines.md`
*   **Location**: Must be placed in the **root directory** of your project.
*   **Format**: Use **Markdown** for clear and organized documentation.

#### 2. What to Include in Your Guidelines (Best Practices)

Keep the document concise and precise. Imagine you're writing it for a talented new engineer joining your team.

**a. High-level Overview**
*   Briefly explain what the project does and its main goals.
*   **Example**:
    ```markdown
    # Project Overview
    This is a web application for managing customer support tickets. The frontend is built with React and the backend is a Node.js API.
    ```

**b. Tech Stack & Key Libraries**
*   Clearly list the main frameworks, libraries, and tools used in the project. This prevents the AI from using technologies you don't want.
*   **Example**:
    ```markdown
    # Tech Stack
    - **Language**: TypeScript
    - **Frontend**: React 18, Next.js 14
    - **Styling**: Tailwind CSS (Do not use plain CSS or CSS-in-JS)
    - **State Management**: Zustand (Do not use Redux or Context API for global state)
    - **Data Fetching**: React Query (use `useQuery` for fetching data and `useMutation` for updates)
    - **Form Handling**: React Hook Form
    - **Validation**: Zod
    ```

**c. Coding Conventions & Style**
*   Define your code style, naming conventions, and other rules.
*   **Example**:
    ```markdown
    # Coding Conventions
    - Use functional components with hooks. Do not use class components.
    - All component file names should be PascalCase (`MyComponent.tsx`).
    - All utility function names should be camelCase (`myFunction.ts`).
    - All API requests must be typed using Zod schemas.
    - Prefer arrow functions for component definitions: `export const MyComponent = () => { ... };`
    ```

**d. Architecture & Design Patterns**
*   Describe the project's directory structure and core design patterns.
*   **Example**:
    ```markdown
    # Architecture
    - All reusable UI components go into `/components/ui`.
    - Feature-specific components go into `/components/features/[feature-name]`.
    - API routes are defined in `/pages/api`.
    - Follow the Atomic Design methodology for components (atoms, molecules, organisms).
    ```

**e. Dos and Don'ts**
*   Use clear lists to specify recommended and discouraged practices.
*   **Example**:
    ```markdown
    # Dos and Don'ts
    - **DO**: Create a Zod schema for every API endpoint's payload.
    - **DO**: Add comments explaining complex business logic.
    - **DON'T**: Write inline styles. Use Tailwind CSS utility classes instead.
    - **DON'T**: Commit API keys or secrets to the repository. Use environment variables.
    ```

**f. How to Add New Features**
*   Provide a simple workflow guide for common tasks.
*   **Example**:
    ```markdown
    # How to add a new API endpoint
    1. Create a new file in `/pages/api/`.
    2. Define the request and response types using Zod schemas in `/lib/schemas.ts`.
    3. Implement the handler function, ensuring proper error handling.
    4. Add an integration test for the new endpoint in the `/tests` directory.
    ```

---

### General Best Practices for Using the Augment Editor

Beyond maintaining the `.augment-guidelines.md` file, here are some general tips for working with Augment:

1.  **Break Down Tasks**: Avoid large, ambiguous commands like "build my app." Instead, break them into smaller, actionable steps, such as "create a main layout file with a header and footer," followed by "add a logo and navigation links to the header."
2.  **Iterate and Interact**: Treat the AI as your pair programming partner. Review the code it generates. If it's not perfect, provide specific feedback like, "This logic is correct, but please refactor it into a separate hook," or "Please replace this native `<button>` with our project's `Button` component."
3.  **Provide Context**: Although Augment reads the entire project and guidelines, specifying the file or code block you're working on can help it focus more quickly.
4.  **Review, Review, Review**: The AI is a powerful tool, but it can still make mistakes. **Never blindly trust AI-generated code.** Always review, understand, and test it yourself before committing.
