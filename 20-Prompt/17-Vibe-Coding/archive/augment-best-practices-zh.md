# Augment 代码编辑器最佳实践

本文档概述了使用 Augment 代码编辑器的最佳实践，并特别重点介绍了 `.augment-guidelines.md` 文件。该文件相当于 AI 的“使用手册”，可以帮助它理解你项目的特定规范和架构。

---

### 核心理念：为 AI 创建一份说明书

`.augment-guidelines.md` 文件的主要目的是为 Augment 的 AI 提供一套持续更新的指令集。当你要求 Augment 执行任务（例如“添加一个新功能”或“重构这个组件”）时，它会首先查阅此文件，以确保其输出符合你项目的标准。这能极大地提高生成代码的质量和一致性。

### 如何使用 `.augment-guidelines.md`

#### 1. 文件位置和格式
*   **文件名**: `.augment-guidelines.md`
*   **位置**: 必须放置在你项目的**根目录**下。
*   **格式**: 使用 **Markdown** 以便清晰地组织文档。

#### 2. 指南中应包含哪些内容（最佳实践）

保持文档简洁而精确。想象一下，你正在为一位才华横溢的新工程师撰写这份入职文档。

**a. 高层概述 (High-level Overview)**
*   简要说明项目��做什么的及其主要目标。
*   **示例**:
    ```markdown
    # 项目概述
    这是一个用于管理客户支持工单的 Web 应用。前端使用 React 构建，后端是一个 Node.js API。
    ```

**b. 技术栈和关键库 (Tech Stack & Key Libraries)**
*   清晰地列出项目使用的主要框架、库和工具。这可以防止 AI 使用你不想用的技术。
*   **示例**:
    ```markdown
    # 技术栈
    - **语言**: TypeScript
    - **前端**: React 18, Next.js 14
    - **样式**: Tailwind CSS (不要使用原生 CSS 或 CSS-in-JS)
    - **状态管理**: Zustand (不要使用 Redux 或 Context API 进行全局状态管理)
    - **数据获取**: React Query (使用 `useQuery` 获取数据，`useMutation` 进行更新)
    - **表单处理**: React Hook Form
    - **校验**: Zod
    ```

**c. 编码规范和风格 (Coding Conventions & Style)**
*   定义你的代码风格、命名约定和其他规则。
*   **示例**:
    ```markdown
    # 编码规范
    - 使用带 hook 的函数式组件。不要使用类组件。
    - 所有组件文件名应为 PascalCase (大驼峰命名法)，例如 `MyComponent.tsx`。
    - 所有工具函数名应为 camelCase (小驼峰命名法)，例如 `myFunction.ts`。
    - 所有 API 请求都必须使用 Zod schema 进行类型定义。
    - 组��定义倾向于使用箭头函数：`export const MyComponent = () => { ... };`
    ```

**d. 架构和设计模式 (Architecture & Design Patterns)**
*   描述项目的目录结构和核心设计模式。
*   **示例**:
    ```markdown
    # 架构
    - 所有可复用的 UI 组件都放在 `/components/ui` 目录下。
    - 特定功能的组件放在 `/components/features/[feature-name]` 目录下。
    - API 路由在 `/pages/api` 中定义。
    - 组件遵循原子设计方法论（原子、分子、组织）。
    ```

**e. “要做”和“不要做” (Dos and Don'ts)**
*   使用清晰的列表来指明推荐和不推荐的做法。
*   **示例**:
    ```markdown
    # “要做”和“不要做”
    - **要做**: 为每个 API 端点的 payload 创建一个 Zod schema。
    - **要做**: 为复杂的业务逻辑添加注释进行解释。
    - **不要做**: 编写内联样式。应使用 Tailwind CSS 的功能类。
    - **不要做**: 将 API 密钥或机密信息提交到代码仓库。应使用环境变量。
    ```

**f. 如何添加新功能 (How to Add New Features)**
*   为常见任务提供一个简单的工作流程指南。
*   **示例**:
    ```markdown
    # 如何添加一个新的 API 端点
    1. 在 `/pages/api/` 目录下创建一个新文件。
    2. 在 `/lib/schemas.ts` 中使用 Zod schema 定义请求和响应的类型。
    3. 实现处理函数，并确保有恰当的错误处理。
    4. 在 `/tests` 目录中为新端点添加一个集成测试。
    ```

---

### 使用 Augment 编辑器的通用最佳实践

除了维护好 `.augment-guidelines.md` 文件外，这里还有一些使用 Augment 的通用技巧：

1.  **分解任务**：避免下达像“构建我的应用”这样宏大而模糊的命令。应将其分解为更小的、可执行的步骤，例如“创建一个包含页眉和页脚的主布局文件”，然后“在页眉中添加 logo 和导航链接”。
2.  **迭代与交互**：将 AI 视为你的结对编程伙伴。审查它生成的代码。如果结果不完美，给出具体的反馈，例如“这个逻辑是对的，但请将其重构为一个独立的 hook”，或者“请用我们项目中的 `Button` 组件替换这个原生的 `<button>` 标签”。
3.  **提供上下文**：虽然 Augment 会读取整个项目和指南，但在提问时，指明你正在处理的文件或代码块可以帮助它更快地聚焦。
4.  **审查、审查、再审查**：AI 是一个强大的工具，但它仍会犯错。**永远不要盲目相信 AI 生成的代码。** 在提交代码之前，一定要亲自审查、理解并测试它。
