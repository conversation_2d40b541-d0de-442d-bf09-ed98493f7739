---
type: guideline
链接: ""
评分: ""
status: active
创建: 20250703113710
更新: 20250703
tags:
  - python
  - best-practices
  - data-processing
  - api-integration
  - code-style
name: Python Best Practices for Data Processing Projects
描述: ""
version: "1.0"
up: ""
down: ""
相关: original
上文:
  - 13-Prompt-Design
mail: ""
next: ""
globs:
  - "*.py"
  - src/**/*.py
alwaysApply: false
---

# Python Best Practices for Data Processing Projects

## Code Style and Formatting
- Follow PEP 8 style guide with 88-character line length (Black formatter standard)
- Use descriptive variable names: `video_data` instead of `vd`
- Use type hints for function parameters and return values
- Example: `def process_video(video_id: str, platform: str) -> Dict[str, Any]:`

## Project Structure (Based on Your Video Factory)
- Keep modules focused on single responsibility (like your src/ structure)
- Use `__init__.py` files to control module exports
- Separate business logic, data processing, and API calls into different modules
- Example structure: `src/business.py`, `src/apis.py`, `src/data_manager.py`

## Error Handling and Logging
- Use structured logging with contextual information
- Implement retry mechanisms for network requests (like your TikHub API calls)
- Use specific exception types: `requests.RequestException`, `json.JSONDecodeError`
- Log with appropriate levels: DEBUG for detailed info, INFO for progress, ERROR for failures

## Configuration Management
- Use environment variables for sensitive data (API keys, tokens)
- Implement configuration validation with Pydantic
- Use `python-dotenv` for local development
- Example: `TIKHUB_API_KEY`, `FEISHU_APP_SECRET`

## Data Processing Best Practices
- Validate data schemas before processing
- Use batch processing for large datasets (like your batch_processor.py)
- Implement proper data serialization/deserialization
- Handle missing or malformed data gracefully

## API Integration
- Implement rate limiting and backoff strategies
- Use session objects for connection pooling
- Validate API responses before processing
- Handle different HTTP status codes appropriately