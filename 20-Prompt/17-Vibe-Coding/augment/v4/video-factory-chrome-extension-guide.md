---
type: agent_requested
描述: Video-Factory项目Chrome插件开发指南，专门针对LLM、飞书API、Midjourney集成
---
# Video-Factory Chrome Extension 开发指南

## 🎯 项目特定需求

### 核心集成目标
- **LLM服务集成**: 对接AI大模型进行视频内容分析
- **飞书API集成**: 读取/写入飞书多维表格数据
- **Midjourney集成**: 批量提交图像生成任务
- **数据流自动化**: 在不同服务间自动传递数据

## 🏗️ 架构设计原则

### 模块化设计
```
Chrome Extension
├── background.js (Service Worker)
│   ├── 飞书API客户端
│   ├── LLM服务客户端
│   └── Midjourney自动化
├── content-scripts/
│   ├── feishu-content.js (飞书页面交互)
│   └── midjourney-content.js (MJ页面交互)
├── popup/
│   ├── 任务控制面板
│   └── 进度监控界面
└── options/
    └── API配置管理
```

### 权限配置
```json
{
  "permissions": [
    "storage",
    "activeTab",
    "scripting"
  ],
  "host_permissions": [
    "https://*.feishu.cn/*",
    "https://*.larksuite.com/*",
    "https://discord.com/*",
    "https://your-llm-service.com/*"
  ]
}
```

## 🔧 核心功能实现

### 1. 飞书数据读取
```javascript
// 从飞书表格读取待处理视频数据
async function fetchVideoData() {
  const response = await fetch('https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records', {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
}
```

### 2. LLM服务调用
```javascript
// 调用AI服务分析视频内容
async function analyzeVideoContent(videoUrl, videoDescription) {
  const prompt = `分析这个视频的内容特点：${videoDescription}`;
  const response = await fetch('your-llm-endpoint', {
    method: 'POST',
    body: JSON.stringify({ prompt, video_url: videoUrl })
  });
  return response.json();
}
```

### 3. Midjourney自动化
```javascript
// 在Discord/MJ中自动提交图像生成任务
function submitMJPrompt(prompt) {
  const inputElement = document.querySelector('[data-slate-editor="true"]');
  inputElement.textContent = `/imagine prompt: ${prompt}`;
  
  // 触发发送
  const sendButton = document.querySelector('[aria-label="Send Message"]');
  sendButton.click();
}
```

## 🔄 数据流管理

### 批处理策略
- **批量大小**: 每次处理5-10个视频，避免API限制
- **错误处理**: 失败任务自动重试，记录错误日志
- **进度跟踪**: 实时更新处理状态到飞书表格

### 状态同步
```javascript
// 更新飞书表格中的处理状态
async function updateProcessStatus(recordId, status, result) {
  await fetch(`https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/${recordId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      fields: {
        '处理状态': status,
        'AI分析结果': result,
        '更新时间': new Date().toISOString()
      }
    })
  });
}
```

## 🛡️ 安全考虑

### API密钥管理
- 使用Chrome Storage API安全存储
- 支持加密存储敏感信息
- 提供密钥轮换机制

### 权限最小化
- 只请求必需的网站权限
- 使用activeTab而非广泛的tabs权限
- 实现细粒度的功能控制

## 🎨 用户体验

### 进度可视化
- 实时显示处理进度条
- 详细的任务状态说明
- 错误信息友好提示

### 配置简化
- 一键导入飞书应用配置
- 自动检测API连接状态
- 提供配置验证功能

## 🧪 测试策略

### 功能测试
- 模拟飞书API响应
- 测试LLM服务集成
- 验证MJ自动化流程

### 性能测试
- 大批量数据处理测试
- 内存使用监控
- 网络请求优化验证

---

**💡 开发重点**: 专注于自动化工作流，减少人工干预，提高Video-Factory整体效率！
