{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-29T16:52:26.371Z", "updatedAt": "2025-07-29T16:52:26.377Z", "resourceCount": 12}, "resources": [{"id": "dialogue-management", "source": "project", "protocol": "execution", "name": "Dialogue Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/dialogue-management.execution.md", "metadata": {"createdAt": "2025-07-29T16:52:26.373Z", "updatedAt": "2025-07-29T16:52:26.373Z", "scannedAt": "2025-07-29T16:52:26.373Z", "path": "role/fury/execution/dialogue-management.execution.md"}}, {"id": "fury-workflow", "source": "project", "protocol": "execution", "name": "Fury Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/fury-workflow.execution.md", "metadata": {"createdAt": "2025-07-29T16:52:26.373Z", "updatedAt": "2025-07-29T16:52:26.373Z", "scannedAt": "2025-07-29T16:52:26.373Z", "path": "role/fury/execution/fury-workflow.execution.md"}}, {"id": "resume-generation", "source": "project", "protocol": "execution", "name": "Resume Generation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/resume-generation.execution.md", "metadata": {"createdAt": "2025-07-29T16:52:26.374Z", "updatedAt": "2025-07-29T16:52:26.374Z", "scannedAt": "2025-07-29T16:52:26.374Z", "path": "role/fury/execution/resume-generation.execution.md"}}, {"id": "fury", "source": "project", "protocol": "role", "name": "Fury 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fury/fury.role.md", "metadata": {"createdAt": "2025-07-29T16:52:26.374Z", "updatedAt": "2025-07-29T16:52:26.374Z", "scannedAt": "2025-07-29T16:52:26.374Z", "path": "role/fury/fury.role.md"}}, {"id": "agent-broker-mindset", "source": "project", "protocol": "thought", "name": "Agent Broker Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fury/thought/agent-broker-mindset.thought.md", "metadata": {"createdAt": "2025-07-29T16:52:26.374Z", "updatedAt": "2025-07-29T16:52:26.374Z", "scannedAt": "2025-07-29T16:52:26.374Z", "path": "role/fury/thought/agent-broker-mindset.thought.md"}}, {"id": "value-discovery-techniques", "source": "project", "protocol": "thought", "name": "Value Discovery Techniques 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fury/thought/value-discovery-techniques.thought.md", "metadata": {"createdAt": "2025-07-29T16:52:26.374Z", "updatedAt": "2025-07-29T16:52:26.374Z", "scannedAt": "2025-07-29T16:52:26.374Z", "path": "role/fury/thought/value-discovery-techniques.thought.md"}}, {"id": "core-management", "source": "project", "protocol": "execution", "name": "Core Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/core-management.execution.md", "metadata": {"createdAt": "2025-07-29T16:52:26.375Z", "updatedAt": "2025-07-29T16:52:26.375Z", "scannedAt": "2025-07-29T16:52:26.375Z", "path": "role/pepper/execution/core-management.execution.md"}}, {"id": "research-planning", "source": "project", "protocol": "execution", "name": "Research Planning 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/research-planning.execution.md", "metadata": {"createdAt": "2025-07-29T16:52:26.375Z", "updatedAt": "2025-07-29T16:52:26.375Z", "scannedAt": "2025-07-29T16:52:26.375Z", "path": "role/pepper/execution/research-planning.execution.md"}}, {"id": "tool-orchestration", "source": "project", "protocol": "execution", "name": "Tool Orchestration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/tool-orchestration.execution.md", "metadata": {"createdAt": "2025-07-29T16:52:26.376Z", "updatedAt": "2025-07-29T16:52:26.376Z", "scannedAt": "2025-07-29T16:52:26.376Z", "path": "role/pepper/execution/tool-orchestration.execution.md"}}, {"id": "pepper", "source": "project", "protocol": "role", "name": "Pepper 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pepper/pepper.role.md", "metadata": {"createdAt": "2025-07-29T16:52:26.376Z", "updatedAt": "2025-07-29T16:52:26.376Z", "scannedAt": "2025-07-29T16:52:26.376Z", "path": "role/pepper/pepper.role.md"}}, {"id": "adaptive-learning", "source": "project", "protocol": "thought", "name": "Adaptive Learning 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/adaptive-learning.thought.md", "metadata": {"createdAt": "2025-07-29T16:52:26.376Z", "updatedAt": "2025-07-29T16:52:26.376Z", "scannedAt": "2025-07-29T16:52:26.376Z", "path": "role/pepper/thought/adaptive-learning.thought.md"}}, {"id": "verification-mindset", "source": "project", "protocol": "thought", "name": "Verification Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/verification-mindset.thought.md", "metadata": {"createdAt": "2025-07-29T16:52:26.377Z", "updatedAt": "2025-07-29T16:52:26.377Z", "scannedAt": "2025-07-29T16:52:26.377Z", "path": "role/pepper/thought/verification-mindset.thought.md"}}], "stats": {"totalResources": 12, "byProtocol": {"execution": 6, "role": 2, "thought": 4}, "bySource": {"project": 12}}}